# *************************************************************
#  Sets up the initial needs to point to our vSphere server
# *************************************************************
# Point to our datacentre
# https://www.terraform.io/docs/providers/vsphere/d/datacenter.html
#
# Notes:
# Datastore Cluster: https://www.terraform.io/docs/providers/vsphere/r/datastore_cluster.html
# Individual Datastores: https://www.terraform.io/docs/providers/vsphere/d/datastore.html
# Resource Pools: https://www.terraform.io/docs/providers/vsphere/d/resource_pool.html
# Networking: https://www.terraform.io/docs/providers/vsphere/d/network.html
# Templates: https://www.terraform.io/docs/providers/vsphere/r/virtual_machine.html
# Folder Managment: https://www.terraform.io/docs/providers/vsphere/r/folder.html
# UUID Usage: https://www.terraform.io/docs/providers/random/r/id.html
# Count Usage: https://www.terraform.io/intro/examples/count.html

# *************************************************************
# Terraform state artifactory backup.
# https://www.terraform.io/docs/backends/types/artifactory.html
# *************************************************************
terraform {
  backend "http" {
  }
}
# *************************************************************
# BCT: bct_ch1_autotest_eala, check CONTRIBUTING.md before editing here.
# *************************************************************

locals {
  module_settings = {
    # lkg_bootanddeploy_tool
    "bct_ch1_lkg_bootanddeploy_tool_001" = { datastore = "esx_build_ssd_ds04_build_vms_03", vm_count = "1", labels = "lkg_bootanddeploy_tool_trunk tool_eala tool", cpu_core = "5" }
    "bct_ch1_lkg_bootanddeploy_tool_002" = { datastore = "esx_build_ssd_ds04_build_vms_03", vm_count = "3", labels = "lkg_bootanddeploy_tool_eala lkg_bootanddeploy_tool tool_eala tool", cpu_core = "5" }

    # STATEBUILD/POOLBUILD AUTOTEST NODES IN EALA
    "ps_autotests_win64_001"       = { datastore = "esx_build_ssd_ds04_build_vms_01", vm_count = "9", labels = "ps_eala statebuild_eala poolbuild_eala win64", cpu_core = "5" }
    "ps_autotests_win64_002"       = { datastore = "esx_build_ssd_ds04_build_vms_04", vm_count = "5", labels = "ps_eala statebuild_eala poolbuild_eala win64", cpu_core = "5" }
    "ps_autotests_ps5_001"         = { datastore = "esx_build_ssd_ds04_build_vms_01", vm_count = "4", labels = "ps_eala statebuild_eala poolbuild_eala ps5", cpu_core = "5" }
    "ps_autotests_ps5_002"         = { datastore = "esx_build_ssd_ds04_build_vms_03", vm_count = "11", labels = "ps_eala statebuild_eala poolbuild_eala ps5", cpu_core = "5" }
    "ps_autotests_xbsx_001"        = { datastore = "esx_build_ssd_ds04_build_vms_01", vm_count = "9", labels = "ps_eala statebuild_eala poolbuild_eala xbsx", cpu_core = "5" }
    "ps_autotests_xbsx_002"        = { datastore = "esx_build_ssd_ds04_build_vms_04", vm_count = "6", labels = "ps_eala statebuild_eala poolbuild_eala xbsx", cpu_core = "5" }
    "ps_autotests_linuxserver_001" = { datastore = "esx_build_ssd_ds04_build_vms_02", vm_count = "2", labels = "ps_eala statebuild_eala poolbuild_eala linuxserver", cpu_core = "6" }
    "ps_autotests_server_001"      = { datastore = "esx_build_ssd_ds04_build_vms_02", vm_count = "2", labels = "ps_eala statebuild_eala poolbuild_eala server", cpu_core = "6" }
    "ps_autotests_linux64_001"     = { datastore = "esx_build_ssd_ds04_build_vms_04", vm_count = "5", labels = "ps_eala statebuild_eala poolbuild_eala linux64", cpu_core = "6" }

    # DEDICATED AUTOTEST NODES IN EALA
    "bct_ch1_lkg_checkmate_win64_001"    = { datastore = "esx_build_ssd_ds04_build_vms_03", vm_count = "2", labels = "ps lkg_checkmate win64_eala", cpu_core = "5" }
    "bct_ch1_lkg_auto_win64_001"         = { datastore = "esx_build_ssd_ds04_build_vms_03", vm_count = "2", labels = "lkg_auto win64_eala", cpu_core = "5" }
    "bct_ch1_lkg_auto_win64_002"         = { datastore = "esx_build_ssd_ds04_build_vms_03", vm_count = "1", labels = "lkg_auto_la_test win64", cpu_core = "5" }
    "bct_ch1_lkg_auto_ps5_eala_001"      = { datastore = "esx_build_ssd_ds04_build_vms_03", vm_count = "2", labels = "lkg_auto ps5_eala", cpu_core = "5" }
    "bct_ch1_lkg_auto_ps5_001"           = { datastore = "esx_build_ssd_ds04_build_vms_03", vm_count = "1", labels = "lkg_auto_la_test ps5", cpu_core = "5" }
    "bct_ch1_lkg_auto_xbsx_eala_001"     = { datastore = "esx_build_ssd_ds04_build_vms_03", vm_count = "2", labels = "lkg_auto xbsx_eala", cpu_core = "5" }
    "bct_ch1_lkg_auto_xbsx_001"          = { datastore = "esx_build_ssd_ds04_build_vms_03", vm_count = "1", labels = "lkg_auto_la_test xbsx", cpu_core = "5" }
    "bct_ch1_unittests_win64_001"        = { datastore = "esx_build_ssd_ds04_build_vms_03", vm_count = "2", labels = "unittests win64_eala", cpu_core = "5" }
    "bct_ch1_unittests_engine_win64_001" = { datastore = "esx_build_ssd_ds04_build_vms_03", vm_count = "1", labels = "unittests_engine tool_eala", cpu_core = "5" }
    "bct_ch1_minspec_win64_001"          = { datastore = "esx_build_ssd_ds04_build_vms_03", vm_count = "1", labels = "recspec poolbuild_eala ps_eala statebuild_eala", cpu_core = "5" }
    "bct_ch1_recspec_win64_001"          = { datastore = "esx_build_ssd_ds04_build_vms_03", vm_count = "1", labels = "minspec poolbuild_eala ps_eala statebuild_eala", cpu_core = "5" }
  }
}

module "dynamic_local_module_primary" {
  source   = "../../modules/windows_attach_module_v3.5"
  for_each = local.module_settings

  vsphere_datastore       = each.value.datastore
  vm_count                = each.value.vm_count
  vm_prefix               = try(each.value.vm_prefix, "bcla-")
  jenkins_slave_labels    = each.value.labels
  role                    = try(each.value.role, "https://bct-ch1-autotest-jenkins.cobra.dre.ea.com/")
  vsphere_compute_cluster = try(each.value.compute_cluster, "Ripple Effect Build Farm")
  ram_count               = try(each.value.ram_count, 65536)
  jenkins_websocket       = try(each.value.jenkins_websocket, "disabled")
  cpu_core                = try(each.value.cpu_core, "")
  cores_per_socket        = try(each.value.cores_per_socket, "")

  cloning_timeout       = var.cloning_timeout
  vsphere_template      = var.packer_template
  vsphere_network       = var.network
  vsphere_datacenter    = var.datacenter
  vsphere_folder        = "DICE/terraform-nodes/bct_ch1_autotest_eala"
  domain_admin          = var.domain_admin
  domain_admin_password = var.domain_password
  local_admin_user      = var.local_username
  local_admin_password  = var.local_password
  local_admin_group     = var.local_admin_group
  project_dir           = var.project_dir
  project_name          = var.project_name
  commit_sha            = var.commit_sha
  commit_user           = var.commit_user
  commit_url            = var.commit_url
  disk_size             = var.disk_size
  domain_name           = var.domain_name
  domain_ou             = "OU=Granite,OU=EALA-Build Servers,DC=la,DC=ad,DC=ea,DC=com"
  hardware_version      = var.hardware_version
}

# *************************************************************
#  Setting up the dynamic output needed for downstream pipelines
# *************************************************************
# Notes:
# For Expressions: https://www.terraform.io/docs/language/expressions/for.html
# Key Functions: https://www.terraform.io/docs/language/functions/keys.html
# flatten Function: https://www.terraform.io/docs/language/functions/flatten.html
# Output Values: https://www.terraform.io/docs/language/values/outputs.html
# Local Values: https://www.terraform.io/docs/language/values/locals.html
#
# The solution outputs the same way as previously in output.tf
# example:
# node_name_uuids = [
#  {
#   "id"   = "JF5D"
#   "name" = "245e43"
#   },
#
# *************************************************************
#  Dynamic Output, check CONTRIBUTING.md before editing here.
# *************************************************************
locals {
  nodes_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].nodes : [
        {
          name              = node.name
          id                = node.id
          custom_attributes = node.custom_attributes
        }
      ]
    ]
  ])
  node_name_uuids_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].node_name_uuids : [
        {
          name = node.hex
          id   = node.id
        }
      ]
    ]
  ])
}

output "nodes" {
  value = local.nodes_output
}

output "node_name_uuids" {
  value = local.node_name_uuids_output
}
